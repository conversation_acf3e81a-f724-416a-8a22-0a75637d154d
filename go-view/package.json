{"name": "go-view", "version": "2.2.9", "engines": {"node": ">=16.14"}, "scripts": {"dev": "vite --host", "build": "vite build", "preview": "vite preview", "new": "plop --plopfile ./plop/plopfile.js", "lint": "eslint --ext .js,.jsx,.ts,.tsx,.vue src", "lint:fix": "eslint --ext .js,.jsx,.ts,.tsx,.vue src --fix"}, "dependencies": {"@amap/amap-jsapi-loader": "^1.0.1", "@amap/amap-jsapi-types": "^0.0.8", "@iconify/json": "^2.2.158", "@types/color": "^3.0.3", "@types/crypto-js": "^4.1.1", "@types/keymaster": "^1.6.30", "@types/lodash": "^4.14.184", "@visactor/vchart": "^2.0.0", "@visactor/vchart-theme": "^1.12.2", "animate.css": "^4.1.1", "axios": "^1.4.0", "color": "^4.2.3", "crypto-js": "^4.1.1", "dayjs": "^1.11.7", "dom-helpers": "^5.2.1", "echarts-liquidfill": "^3.1.0", "echarts-stat": "^1.2.0", "echarts-wordcloud": "^2.0.0", "gsap": "^3.11.3", "highlight.js": "^11.5.0", "html2canvas": "^1.4.1", "iconify-icon": "^1.0.8", "keymaster": "^1.6.2", "mitt": "^3.0.0", "monaco-editor": "^0.33.0", "naive-ui": "2.40.3", "pinia": "^2.0.13", "screenfull": "^6.0.1", "three": "^0.145.0", "vue": "^3.5.13", "vue-demi": "^0.13.1", "vue-i18n": "9.2.2", "vue-router": "4.0.12", "vue3-lazyload": "^0.2.5-beta", "vue3-sketch-ruler": "1.3.3", "vuedraggable": "^4.1.0"}, "devDependencies": {"@commitlint/cli": "^17.0.2", "@commitlint/config-conventional": "^17.0.2", "@types/node": "^16.11.26", "@types/three": "^0.144.0", "@typescript-eslint/eslint-plugin": "^5.18.0", "@typescript-eslint/parser": "^5.18.0", "@vicons/carbon": "^0.12.0", "@vicons/ionicons5": "~0.11.0", "@vitejs/plugin-vue": "^4.2.3", "@vitejs/plugin-vue-jsx": "^3.0.1", "@vue/compiler-sfc": "^3.2.31", "@vueuse/core": "^7.7.1", "commitlint": "^17.0.2", "default-passive-events": "^2.0.0", "echarts": "^5.3.2", "eslint": "^8.12.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-import": "^2.26.0", "eslint-plugin-prettier": "^4.0.0", "eslint-plugin-vue": "^8.5.0", "husky": "^8.0.1", "lodash": "~4.17.21", "mockjs": "^1.1.0", "plop": "^3.0.5", "prettier": "^2.6.2", "sass": "1.49.11", "sass-loader": "^12.6.0", "typescript": "4.6.3", "vite": "4.3.6", "vite-plugin-compression": "^0.5.1", "vite-plugin-importer": "^0.2.5", "vite-plugin-mock": "^2.9.6", "vite-plugin-monaco-editor": "^1.1.0", "vue-echarts": "^6.0.2", "vue-tsc": "^0.28.10"}}