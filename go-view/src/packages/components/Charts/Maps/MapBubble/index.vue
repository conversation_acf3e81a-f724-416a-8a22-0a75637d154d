<template>
  <div>
    <div class="back-icon" v-if="(enter && levelHistory.length !== 0) || (enter && !isPreview())" @click="backLevel">
      <n-icon :color="backColor" :size="backSize * 1.1">
        <ArrowBackIcon />
      </n-icon>
      <span
        :style="{
          'font-weight': 200,
          color: backColor,
          'font-size': `${backSize}px`
        }"
      >
        返回上级
      </span>
    </div>
    <v-chart
      ref="vChartRef"
      :init-options="initOptions"
      :theme="themeColor"
      :option="option.value"
      :manual-update="isPreview()"
      autoresize
      @click="chartPEvents"
    >
    </v-chart>
  </div>
</template>

<script setup lang="ts">
import { PropType, reactive, watch, ref, nextTick, toRefs } from 'vue'
import config, { includes } from './config'
import VChart from 'vue-echarts'
import { icon } from '@/plugins'
import { useCanvasInitOptions } from '@/hooks/useCanvasInitOptions.hook'
import { use, registerMap } from 'echarts/core'
import { EffectScatterChart, MapChart } from 'echarts/charts'
import { CanvasRenderer } from 'echarts/renderers'
import { useChartDataFetch } from '@/hooks'
import { mergeTheme, setOption } from '@/packages/public/chart'
import { useChartEditStore } from '@/store/modules/chartEditStore/chartEditStore'
import { isPreview } from '@/utils'
import mapJsonWithoutHainanIsLands from '../MapBase/mapWithoutHainanIsLands.json'
import mapChinaJson from '../MapBase/mapGeojson/china.json'
import { DatasetComponent, GridComponent, TooltipComponent, GeoComponent, VisualMapComponent } from 'echarts/components'

const props = defineProps({
  themeSetting: {
    type: Object,
    required: true
  },
  themeColor: {
    type: Object,
    required: true
  },
  chartConfig: {
    type: Object as PropType<config>,
    required: true
  }
})

const { ArrowBackIcon } = icon.ionicons5
let levelHistory: any = ref([])

const { backColor, backSize, enter } = toRefs(props.chartConfig.option.mapRegion)
const initOptions = useCanvasInitOptions(props.chartConfig.option, props.themeSetting)

use([
  MapChart,
  DatasetComponent,
  CanvasRenderer,
  GridComponent,
  TooltipComponent,
  GeoComponent,
  EffectScatterChart,
  VisualMapComponent
])

const option = reactive({
  value: mergeTheme(props.chartConfig.option, props.themeSetting, includes)
})
const vChartRef = ref<typeof VChart>()

//动态获取json注册地图
const getGeojson = (regionId: string) => {
  return new Promise<boolean>(resolve => {
    import(`../MapBase/mapGeojson/${regionId}.json`).then(data => {
      registerMap(regionId, { geoJSON: data.default as any, specialAreas: {} })
      resolve(true)
    })
  })
}

//异步时先注册空的 保证初始化不报错
registerMap(`${props.chartConfig.option.mapRegion.adcode}`, { geoJSON: {} as any, specialAreas: {} })

// 进行更换初始化地图 如果为china 单独处理
const registerMapInitAsync = async () => {
  console.log('🗺️ [MapBubble] 开始初始化地图')
  await nextTick()
  const adCode = `${props.chartConfig.option.mapRegion.adcode}`
  console.log('🌍 [MapBubble] 地图区域代码:', adCode)

  if (adCode !== 'china') {
    await getGeojson(adCode)
  } else {
    await hainanLandsHandle(props.chartConfig.option.mapRegion.showHainanIsLands)
  }

  console.log('🗺️ [MapBubble] 地图注册完成，开始渲染')
  // 遵循 MapBase 设计：初始化后直接渲染
  vEchartsSetOption()
}
registerMapInitAsync()

// 应用气泡和标签配置到 series
const applyBubbleAndLabelConfig = () => {
  console.log('🔧 [MapBubble] applyBubbleAndLabelConfig 开始执行')

  props.chartConfig.option.series.forEach((item: any, index: number) => {
    if (item.type === 'effectScatter') {
      console.log(`🎯 [MapBubble] 处理 effectScatter series[${index}]`)

      // 应用气泡配置
      const bubbleConfig = props.chartConfig.option.bubbleConfig
      if (bubbleConfig) {
        console.log('🎨 [MapBubble] 应用气泡配置 - 完整配置:', JSON.stringify(bubbleConfig, null, 2))

        // 统一气泡大小
        item.symbolSize = bubbleConfig.symbolSize || 30
        console.log('📏 [MapBubble] 设置统一气泡大小:', bubbleConfig.symbolSize)

        // 在 series 级别设置 itemStyle
        const seriesItemStyle = {
          color: bubbleConfig.bubbleColor,
          borderColor: bubbleConfig.borderColor,
          borderWidth: bubbleConfig.borderWidth,
          shadowColor: bubbleConfig.shadowColor || bubbleConfig.bubbleColor,
          shadowBlur: bubbleConfig.shadowBlur
        }

        item.itemStyle = seriesItemStyle
        console.log('✅ [MapBubble] series 级别 itemStyle 已设置:', JSON.stringify(seriesItemStyle, null, 2))

        // 同时在每个数据点上设置 itemStyle
        if (item.data && Array.isArray(item.data)) {
          item.data.forEach((dataPoint: any, dataIndex: number) => {
            dataPoint.itemStyle = {
              color: bubbleConfig.bubbleColor,
              borderColor: bubbleConfig.borderColor,
              borderWidth: bubbleConfig.borderWidth,
              shadowColor: bubbleConfig.shadowColor || bubbleConfig.bubbleColor,
              shadowBlur: bubbleConfig.shadowBlur
            }
            console.log(`✅ [MapBubble] 数据点 ${dataIndex} itemStyle 已设置:`, JSON.stringify(dataPoint.itemStyle, null, 2))
          })
        }

        if (bubbleConfig.showEffect) {
          item.rippleEffect = {
            scale: bubbleConfig.effectScale,
            color: bubbleConfig.effectColor,
            brushType: 'fill'
          }
          console.log('🌊 [MapBubble] 波纹效果已设置:', item.rippleEffect)
        }
      } else {
        console.warn('⚠️ [MapBubble] bubbleConfig 不存在')
      }

      // 应用标签配置
      const labelConfig = props.chartConfig.option.labelConfig
      if (labelConfig) {
        console.log('🏷️ [MapBubble] 应用标签配置:', {
          show: labelConfig.show,
          color: labelConfig.color,
          fontSize: labelConfig.fontSize
        })

        item.label = {
          show: labelConfig.show,
          fontSize: labelConfig.fontSize,
          color: labelConfig.color,
          fontWeight: labelConfig.fontWeight,
          position: labelConfig.position,
          offset: labelConfig.offset,
          textBorderColor: labelConfig.textBorderColor,
          textBorderWidth: labelConfig.textBorderWidth,
          textShadowColor: labelConfig.textShadowColor,
          textShadowBlur: labelConfig.textShadowBlur,
          formatter: function(params: any) {
            return params.data.label || params.data.name
          }
        }
      }
    }
  })

  console.log('✅ [MapBubble] applyBubbleAndLabelConfig 执行完成')
}

// 手动触发渲染
const vEchartsSetOption = () => {
  // 在设置 option 之前先应用气泡和标签配置
  applyBubbleAndLabelConfig()

  option.value = props.chartConfig.option
  setOption(vChartRef.value, props.chartConfig.option)

  // 强制刷新图表
  nextTick(() => {
    if (vChartRef.value) {
      console.log('🔄 [MapBubble] 强制刷新图表')
      vChartRef.value.setOption(props.chartConfig.option, true)
    }
  })
}

// 更新数据处理
const dataSetHandle = async (dataset: any) => {
  props.chartConfig.option.series.forEach((item: any) => {
    if (item.type === 'effectScatter' && dataset) {
      const mappedData = dataset.map((dataItem: any) => ({
        name: dataItem.name,
        value: dataItem.value,
        label: dataItem.label || dataItem.name
      }))
      item.data = mappedData
    }
  })

  isPreview() && vEchartsSetOption()
}

// 处理海南群岛
const hainanLandsHandle = async (newData: boolean) => {
  if (newData) {
    await getGeojson('china')
  } else {
    registerMap('china', { geoJSON: mapJsonWithoutHainanIsLands as any, specialAreas: {} })
  }
}

// 点击区域
const chartPEvents = (e: any) => {
  if (e.seriesType !== 'map') return
  if (!props.chartConfig.option.mapRegion.enter) {
    return
  }
  mapChinaJson.features.forEach(item => {
    var pattern = new RegExp(e.name)
    if (pattern.test(item.properties.name)) {
      let code = String(item.properties.adcode)
      levelHistory.value.push(code)
      checkOrMap(code)
    }
  })
}

// 返回上一级
const backLevel = () => {
  levelHistory.value = []
  if (levelHistory.value.length > 1) {
    levelHistory.value.pop()
    const code = levelHistory[levelHistory.value.length - 1]
    checkOrMap(code)
  } else {
    checkOrMap('china')
  }
}

// 切换地图
const checkOrMap = async (newData: string) => {
  if (newData === 'china') {
    if (props.chartConfig.option.mapRegion.showHainanIsLands) {
      hainanLandsHandle(true)
      vEchartsSetOption()
    } else {
      hainanLandsHandle(false)
      vEchartsSetOption()
    }
  } else {
    await getGeojson(newData)
  }
  props.chartConfig.option.geo.map = newData
  props.chartConfig.option.series.forEach((item: any) => {
    if (item.type === 'effectScatter') item.coordinateSystem = 'geo'
  })
  vEchartsSetOption()
}

watch(
  () => props.chartConfig.option.mapRegion.adcode,
  (newData: string) => {
    checkOrMap(newData)
  }
)

watch(
  () => props.chartConfig.option.mapRegion.showHainanIsLands,
  (newData: boolean) => {
    if (props.chartConfig.option.mapRegion.adcode === 'china') {
      hainanLandsHandle(newData)
      vEchartsSetOption()
    }
  }
)

// 预览
useChartDataFetch(props.chartConfig, useChartEditStore, (newData: any) => {
  dataSetHandle(newData)
})

// 监听数据变化
watch(
  () => props.chartConfig.option.dataset,
  (newData: any) => {
    if (newData && newData.length > 0) {
      dataSetHandle(newData)
    }
  },
  { deep: true, immediate: true }
)

// 监听气泡配置变化
if (!isPreview()) {
  watch(
    () => props.chartConfig.option.bubbleConfig,
    (newConfig, oldConfig) => {
      console.log('🔄 [MapBubble] 气泡配置变化，重新渲染')
      console.log('新配置:', newConfig)
      vEchartsSetOption()
    },
    { deep: true, immediate: false }
  )
}

// 监听标签配置变化
if (!isPreview()) {
  watch(
    () => props.chartConfig.option.labelConfig,
    (newConfig, oldConfig) => {
      console.log('🔄 [MapBubble] 标签配置变化，重新渲染')
      console.log('新配置:', newConfig)
      vEchartsSetOption()
    },
    { deep: true, immediate: false }
  )
}

// 监听地图配置变化（包括海南诸岛等）
if (!isPreview()) {
  watch(
    () => props.chartConfig.option.mapRegion,
    (newConfig, oldConfig) => {
      console.log('🔄 [MapBubble] 地图配置变化，重新渲染')
      console.log('新配置:', newConfig)
      if (newConfig.adcode !== oldConfig?.adcode) {
        checkOrMap(newConfig.adcode)
      }
      if (newConfig.showHainanIsLands !== oldConfig?.showHainanIsLands && newConfig.adcode === 'china') {
        hainanLandsHandle(newConfig.showHainanIsLands)
      }
      vEchartsSetOption()
    },
    { deep: true, immediate: false }
  )
}
</script>

<style lang="scss" scoped>
.back-icon {
  position: absolute;
  top: 10px;
  left: 10px;
  z-index: 999;
  display: flex;
  align-items: center;
  cursor: pointer;
  user-select: none;
  
  span {
    margin-left: 5px;
  }
}
</style>
