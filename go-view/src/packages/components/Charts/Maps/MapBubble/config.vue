<template>
  <!-- Echarts 全局设置 -->
  <global-setting :optionData="optionData"></global-setting>
  
  <CollapseItem name="地图" :expanded="true">
    <SettingItemBox name="地图区域">
      <SettingItem name="默认中国">
        <n-select
          size="small"
          v-model:value="mapRegion.adcode"
          :options="mapRegionOptions"
          value-field="adcode"
          label-field="name"
        />
      </SettingItem>
      <SettingItem name="显示南海诸岛">
        <n-switch v-model:value="mapRegion.showHainanIsLands" size="small" />
      </SettingItem>
      <SettingItem name="支持下钻">
        <n-switch v-model:value="mapRegion.enter" size="small" />
      </SettingItem>
    </SettingItemBox>

    <SettingItemBox name="区域颜色">
      <SettingItem name="0%处颜色">
        <n-color-picker
          size="small"
          :modes="['hex']"
          v-model:value="geo.itemStyle.areaColor.colorStops[0].color"
        />
      </SettingItem>
      <SettingItem name="100%处颜色">
        <n-color-picker
          size="small"
          :modes="['hex']"
          v-model:value="geo.itemStyle.areaColor.colorStops[1].color"
        />
      </SettingItem>
    </SettingItemBox>

    <SettingItemBox name="区域边框">
      <SettingItem name="边框颜色">
        <n-color-picker
          size="small"
          :modes="['hex']"
          v-model:value="geo.itemStyle.borderColor"
        />
      </SettingItem>
      <SettingItem name="边框宽度">
        <n-input-number
          v-model:value="geo.itemStyle.borderWidth"
          :min="0"
          :max="10"
          size="small"
        />
      </SettingItem>
    </SettingItemBox>

    <SettingItemBox name="阴影">
      <SettingItem name="阴影颜色">
        <n-color-picker
          size="small"
          :modes="['hex']"
          v-model:value="geo.itemStyle.shadowColor"
        />
      </SettingItem>
      <SettingItem name="阴影模糊">
        <n-input-number
          v-model:value="geo.itemStyle.shadowBlur"
          :min="0"
          :max="20"
          size="small"
        />
      </SettingItem>
    </SettingItemBox>

    <SettingItemBox name="悬停样式">
      <SettingItem name="悬停颜色">
        <n-color-picker
          size="small"
          :modes="['hex']"
          v-model:value="geo.emphasis.itemStyle.areaColor"
        />
      </SettingItem>
      <SettingItem name="悬停阴影">
        <n-color-picker
          size="small"
          :modes="['hex']"
          v-model:value="geo.emphasis.itemStyle.shadowColor"
        />
      </SettingItem>
    </SettingItemBox>
  </CollapseItem>

  <CollapseItem name="气泡配置" :expanded="true">
    <SettingItemBox name="气泡样式">
      <SettingItem name="最小尺寸">
        <n-input-number v-model:value="bubbleConfig.symbolSize[0]" size="small" :min="10" :max="100" />
      </SettingItem>
      <SettingItem name="最大尺寸">
        <n-input-number v-model:value="bubbleConfig.symbolSize[1]" size="small" :min="20" :max="200" />
      </SettingItem>
      <SettingItem name="气泡颜色">
        <n-color-picker v-model:value="bubbleConfig.bubbleColor" size="small" />
      </SettingItem>
      <SettingItem name="边框颜色">
        <n-color-picker v-model:value="bubbleConfig.borderColor" size="small" />
      </SettingItem>
      <SettingItem name="边框宽度">
        <n-input-number v-model:value="bubbleConfig.borderWidth" size="small" :min="0" :max="10" />
      </SettingItem>
      <SettingItem name="阴影模糊">
        <n-input-number v-model:value="bubbleConfig.shadowBlur" size="small" :min="0" :max="20" />
      </SettingItem>
    </SettingItemBox>

    <SettingItemBox name="动画效果">
      <SettingItem name="显示动画">
        <n-switch v-model:value="bubbleConfig.showEffect" size="small" />
      </SettingItem>
      <SettingItem name="动画缩放" v-if="bubbleConfig.showEffect">
        <n-input-number v-model:value="bubbleConfig.effectScale" size="small" :min="2" :max="20" />
      </SettingItem>
      <SettingItem name="动画颜色" v-if="bubbleConfig.showEffect">
        <n-color-picker v-model:value="bubbleConfig.effectColor" size="small" />
      </SettingItem>
    </SettingItemBox>
  </CollapseItem>

  <CollapseItem name="标签配置" :expanded="true">
    <SettingItemBox name="标签显示">
      <SettingItem name="显示标签">
        <n-switch v-model:value="labelConfig.show" size="small" />
      </SettingItem>
      <SettingItem name="标签位置" v-if="labelConfig.show">
        <n-select
          size="small"
          v-model:value="labelConfig.position"
          :options="labelPositionOptions"
        />
      </SettingItem>
      <SettingItem name="字体大小" v-if="labelConfig.show">
        <n-input-number v-model:value="labelConfig.fontSize" size="small" :min="8" :max="24" />
      </SettingItem>
      <SettingItem name="字体颜色" v-if="labelConfig.show">
        <n-color-picker v-model:value="labelConfig.color" size="small" />
      </SettingItem>
      <SettingItem name="字体粗细" v-if="labelConfig.show">
        <n-select
          size="small"
          v-model:value="labelConfig.fontWeight"
          :options="fontWeightOptions"
        />
      </SettingItem>
    </SettingItemBox>

    <SettingItemBox name="标签边框" v-if="labelConfig.show">
      <SettingItem name="边框颜色">
        <n-color-picker v-model:value="labelConfig.textBorderColor" size="small" />
      </SettingItem>
      <SettingItem name="边框宽度">
        <n-input-number v-model:value="labelConfig.textBorderWidth" size="small" :min="0" :max="5" />
      </SettingItem>
      <SettingItem name="阴影颜色">
        <n-color-picker v-model:value="labelConfig.textShadowColor" size="small" />
      </SettingItem>
      <SettingItem name="阴影模糊">
        <n-input-number v-model:value="labelConfig.textShadowBlur" size="small" :min="0" :max="10" />
      </SettingItem>
    </SettingItemBox>
  </CollapseItem>

  <CollapseItem name="提示框" :expanded="false">
    <SettingItemBox name="提示框样式">
      <SettingItem name="显示提示框">
        <n-switch v-model:value="tooltip.show" size="small" />
      </SettingItem>
      <SettingItem name="背景颜色" v-if="tooltip.show">
        <n-color-picker v-model:value="tooltip.backgroundColor" size="small" />
      </SettingItem>
      <SettingItem name="边框颜色" v-if="tooltip.show">
        <n-color-picker v-model:value="tooltip.borderColor" size="small" />
      </SettingItem>
      <SettingItem name="边框宽度" v-if="tooltip.show">
        <n-input-number v-model:value="tooltip.borderWidth" size="small" :min="0" :max="5" />
      </SettingItem>
    </SettingItemBox>
  </CollapseItem>
</template>

<script setup lang="ts">
import { PropType, computed, ref } from 'vue'
import { CollapseItem, SettingItemBox, SettingItem } from '@/components/Pages/ChartItemSetting'
import { GlobalThemeJsonType } from '@/settings/chartThemes/index'
import { GlobalSetting } from '@/components/Pages/ChartItemSetting'

const mapRegionOptions = ref([
  {
    adcode: 'china',
    name: '中国'
  }
])

const labelPositionOptions = ref([
  { value: 'top', label: '上方' },
  { value: 'bottom', label: '下方' },
  { value: 'left', label: '左侧' },
  { value: 'right', label: '右侧' },
  { value: 'inside', label: '内部' }
])

const fontWeightOptions = ref([
  { value: 'normal', label: '正常' },
  { value: 'bold', label: '粗体' },
  { value: 'bolder', label: '更粗' },
  { value: 'lighter', label: '更细' }
])

const props = defineProps({
  optionData: {
    type: Object as PropType<GlobalThemeJsonType>,
    required: true
  }
})

const optionData = computed(() => {
  return props.optionData
})

const mapRegion = computed(() => {
  return optionData.value.mapRegion
})

const geo = computed(() => {
  return optionData.value.geo
})

const bubbleConfig = computed({
  get() {
    return optionData.value.bubbleConfig
  },
  set(value) {
    Object.assign(optionData.value.bubbleConfig, value)
  }
})

const labelConfig = computed({
  get() {
    return optionData.value.labelConfig
  },
  set(value) {
    Object.assign(optionData.value.labelConfig, value)
  }
})

const tooltip = computed(() => {
  return optionData.value.tooltip
})
</script>
