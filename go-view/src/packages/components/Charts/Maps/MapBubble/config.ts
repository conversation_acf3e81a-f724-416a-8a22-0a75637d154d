import { echartOptionProfixHandle, PublicConfigClass } from '@/packages/public'
import { MapBubbleConfig } from './index'
import { chartInitConfig } from '@/settings/designSetting'
import { CreateComponentType } from '@/packages/index.d'
import cloneDeep from 'lodash/cloneDeep'
import dataJson from './data.json'

export const includes = []

export const option = {
  dataset: dataJson,
  mapRegion: {
    adcode: 'china',
    showHainanIsLands: true,
    enter: false,
    backSize: 20,
    backColor: '#ffffff'
  },
  tooltip: {
    show: true,
    trigger: 'item',
    backgroundColor: 'rgba(0,0,0,0.8)',
    borderColor: 'rgba(147, 235, 248, 0.8)',
    borderWidth: 1,
    textStyle: {
      color: '#FFFFFF',
      fontSize: 14
    }
  },
  visualMap: {
    show: true,
    orient: 'vertical',
    pieces: [
      { gte: 1000, label: '>1000' },
      { gte: 600, lte: 999, label: '600-999' },
      { gte: 200, lte: 599, label: '200-599' },
      { gte: 50, lte: 199, label: '49-199' },
      { gte: 10, lte: 49, label: '10-49' },
      { lte: 9, label: '<9' }
    ],
    inRange: {
      color: ['#c3d7df', '#5cb3cc', '#8abcd1', '#66a9c9', '#2f90b9', '#1781b5']
    },
    textStyle: {
      color: '#fff'
    }
  },
  geo: {
    show: true,
    type: 'map',
    roam: true,
    map: 'china',
    selectedMode: false,
    zoom: 1,
    itemStyle: {
      borderColor: '#93EBF8',
      borderWidth: 1,
      areaColor: {
        type: 'radial',
        x: 0.5,
        y: 0.5,
        r: 0.8,
        colorStops: [
          {
            offset: 0,
            color: '#c3d7df'
          },
          {
            offset: 1,
            color: '#5cb3cc'
          }
        ],
        globalCoord: false
      },
      shadowColor: '#5cb3cc42',
      shadowOffsetX: -2,
      shadowOffsetY: 2,
      shadowBlur: 10
    },
    emphasis: {
      itemStyle: {
        areaColor: '#2a8aa3',
        shadowColor: '#2a8aa3',
        borderWidth: 1
      }
    }
  },
  bubbleConfig: {
    symbolSize: 30,
    showEffect: true,
    effectScale: 6,
    effectColor: '#81d4fa',
    bubbleColor: '#FF6B6B',
    borderColor: '#FFFFFF',
    borderWidth: 2,
    shadowBlur: 10,
    shadowColor: '#FF6B6B'
  },
  labelConfig: {
    show: true,
    position: 'top',
    fontSize: 12,
    color: '#FFFFFF',
    fontWeight: 'bold',
    textBorderColor: '#000000',
    textBorderWidth: 1,
    textShadowColor: '#000000',
    textShadowBlur: 3,
    offset: [0, -10]
  },
  series: [
    {
      name: '区域',
      type: 'map',
      map: 'china',
      data: [],
      selectedMode: false,
      zoom: 1,
      geoIndex: 1,
      tooltip: {
        show: true,
        backgroundColor: '#00000060',
        borderColor: 'rgba(147, 235, 248, 0.8)',
        textStyle: {
          color: '#FFFFFF',
          fontSize: 12
        }
      },
      label: {
        show: false,
        color: '#FFFFFF',
        fontSize: 12
      },
      emphasis: {
        disabled: false,
        label: {
          color: '#FFFFFF',
          fontSize: 12
        },
        itemStyle: {
          areaColor: '#389BB7',
          shadowColor: '#389BB7',
          borderWidth: 1
        }
      },
      itemStyle: {
        borderColor: '#93EBF8',
        borderWidth: 1,
        areaColor: {
          type: 'radial',
          x: 0.5,
          y: 0.5,
          r: 0.8,
          colorStops: [
            {
              offset: 0,
              color: '#93ebf800'
            },
            {
              offset: 1,
              color: '#93ebf820'
            }
          ],
          globalCoord: false
        },
        shadowColor: '#80D9F842',
        shadowOffsetX: -2,
        shadowOffsetY: 2,
        shadowBlur: 10
      }
    },
    {
      name: '气泡标注',
      type: 'effectScatter',
      coordinateSystem: 'geo',
      symbolSize: 30,
      showEffectOn: 'render',
      rippleEffect: {
        scale: 6,
        color: '#FFFFFF',
        brushType: 'fill'
      },
      tooltip: {
        show: true,
        formatter: function(params: any) {
          return params.data.label || `${params.data.name}: ${params.data.value[2]}`
        }
      },
      label: {
        show: true,
        formatter: function(params: any) {
          return params.data.label || params.data.name
        },
        fontSize: 12,
        color: '#FFFFFF',
        fontWeight: 'bold',
        position: 'top',
        offset: [0, -10],
        textBorderColor: '#000000',
        textBorderWidth: 1,
        textShadowColor: '#000000',
        textShadowBlur: 3
      },
      itemStyle: {
        color: '#FF6B6B',
        borderColor: '#FFFFFF',
        borderWidth: 2,
        shadowColor: '#FF6B6B',
        shadowBlur: 10
      },
      data: [],
      encode: {
        value: 2
      }
    }
  ]
}

export const MapBubbleDefaultConfig = { ...option }

export default class Config extends PublicConfigClass implements CreateComponentType {
  public key: string = MapBubbleConfig.key
  public attr = { ...chartInitConfig, w: 750, h: 800, zIndex: -1 }
  public chartConfig = cloneDeep(MapBubbleConfig)
  public option = echartOptionProfixHandle(option, includes)
}
